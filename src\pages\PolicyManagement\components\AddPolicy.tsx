import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../../components/Button';
import Dropdown from '../../../components/Dropdown';

const AddPolicy = () => {
  const navigate = useNavigate();
  
  const [policyName, setPolicyName] = useState('');
  const [description, setDescription] = useState('');
  const [maxFrequency, setMaxFrequency] = useState('');
  const [receiptRequirement, setReceiptRequirement] = useState('');
  const [departments, setDepartments] = useState('');
  const [categories, setCategories] = useState('');
  const [maxAmount, setMaxAmount] = useState('');
  const [perDiem, setPerDiem] = useState('');
  const [destinationTypes, setDestinationTypes] = useState('');
  const [bookingClass, setBookingClass] = useState('');
  const [advanceBooking, setAdvanceBooking] = useState('');

  const receiptOptions = ['Required', 'Optional', 'Not Required'];
  const departmentOptions = ['All Departments', 'Finance', 'Marketing', 'Engineering', 'HR'];
  const categoryOptions = ['All Categories', 'Travel', 'Meals', 'Accommodation', 'Office Supplies'];
  const destinationOptions = ['Domestic', 'International', 'Both'];
  const bookingClassOptions = ['Economy', 'Business', 'First Class'];
  const advanceBookingOptions = ['Not Required', '1 Week', '2 Weeks', '1 Month'];

  const handleCancel = () => {
    navigate('/policies');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Process form submission
    console.log({
      policyName,
      description,
      maxFrequency,
      receiptRequirement,
      departments,
      categories,
      maxAmount,
      perDiem,
      destinationTypes,
      bookingClass,
      advanceBooking
    });
    
    // Navigate back to policies page after submission
    navigate('/policies');
  };

  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      {/* Header with breadcrumb */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Policy Management</h1>
          <div className="flex items-center">
            <span 
              className="cursor-pointer hover:text-teal-600 transition-colors text-sm text-gray-500 font-semibold"
              onClick={() => navigate('/')}
            >
              Dashboard
            </span>
            <span className="mx-2 text-sm text-gray-500">/</span>
            <span 
              className="cursor-pointer hover:text-teal-600 transition-colors text-sm text-gray-500 font-semibold"
              onClick={() => navigate('/policies')}
            >
              Policy Management
            </span>
            <span className="mx-2 text-sm text-gray-500">/</span>
            <span className="text-sm text-teal-600 font-semibold">
              New Policy
            </span>
          </div>
        </div>
        <Button
          className="bg-teal-700 hover:bg-teal-800 px-4 py-2 rounded-md text-sm text-white"
          onClick={handleCancel}
        >
          Cancel Policy
        </Button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left Column - Policy Information */}
          <div>
            <h2 className="text-lg font-semibold text-gray-600 mb-6">Policy Information</h2>
            
            <div className="mb-6">
              <label htmlFor="policyName" className="block text-sm font-medium text-gray-600 mb-2">
                Policy Name
              </label>
              <input
                type="text"
                id="policyName"
                placeholder="Enter policy name"
                value={policyName}
                onChange={(e) => setPolicyName(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-md focus:ring-teal-500 focus:border-teal-500"
              />
            </div>

            <div className="mb-6">
              <label htmlFor="description" className="block text-sm font-medium text-gray-600 mb-2">
                Description
              </label>
              <textarea
                id="description"
                rows={5}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-md focus:ring-teal-500 focus:border-teal-500"
              />
            </div>

            <div className="mb-6">
              <label htmlFor="maxFrequency" className="block text-sm font-medium text-gray-600 mb-2">
                Maximum Frequency
              </label>
              <input
                type="text"
                id="maxFrequency"
                placeholder="Enter frequency"
                value={maxFrequency}
                onChange={(e) => setMaxFrequency(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-md focus:ring-teal-500 focus:border-teal-500"
              />
            </div>

            <div className="mb-6">
              <label htmlFor="receiptRequirement" className="block text-sm font-medium text-gray-600 mb-2">
                Receipt Requirement
              </label>
              <Dropdown
                value={receiptRequirement}
                onChange={setReceiptRequirement}
                options={receiptOptions}
                label="Receipt Requirement"
                placeholder="Select requirement"
              />
            </div>

            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-600 mb-2">Applicability (Optional)</h3>
              
              <div className="mt-4">
                <label htmlFor="departments" className="block text-sm font-medium text-gray-600 mb-2">
                  Applicable Departments
                </label>
                <Dropdown
                  value={departments}
                  onChange={setDepartments}
                  options={departmentOptions}
                  label="Departments"
                  placeholder="Select departments"
                />
              </div>
            </div>
          </div>

          {/* Right Column - Policy Rules/Guidelines */}
          <div>
            <h2 className="text-lg font-semibold text-gray-600 mb-6">Policy Rules/Guidelines</h2>
            
            <div className="mb-6">
              <label htmlFor="categories" className="block text-sm font-medium text-gray-600 mb-2">
                Expense Categories
              </label>
              <Dropdown
                value={categories}
                onChange={setCategories}
                options={categoryOptions}
                label="Categories"
                placeholder="Select categories"
              />
            </div>

            <div className="mb-6">
              <label htmlFor="maxAmount" className="block text-sm font-medium text-gray-600 mb-2">
                Maximum Amount
              </label>
              <input
                type="text"
                id="maxAmount"
                placeholder="Enter amount"
                value={maxAmount}
                onChange={(e) => setMaxAmount(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-md focus:ring-teal-500 focus:border-teal-500"
              />
            </div>

            <div className="mb-6">
              <label htmlFor="perDiem" className="block text-sm font-medium text-gray-600 mb-2">
                Per Diem
              </label>
              <input
                type="text"
                id="perDiem"
                placeholder="Enter amount"
                value={perDiem}
                onChange={(e) => setPerDiem(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-md focus:ring-teal-500 focus:border-teal-500"
              />
            </div>

            <div className="mb-6">
              <label htmlFor="destinationTypes" className="block text-sm font-medium text-gray-600 mb-2">
                Destination Types (Travel)
              </label>
              <Dropdown
                value={destinationTypes}
                onChange={setDestinationTypes}
                options={destinationOptions}
                label="Destination Types"
                placeholder="Select types"
              />
            </div>

            <div className="mb-6">
              <label htmlFor="bookingClass" className="block text-sm font-medium text-gray-600 mb-2">
                Booking Class (Travel)
              </label>
              <Dropdown
                value={bookingClass}
                onChange={setBookingClass}
                options={bookingClassOptions}
                label="Booking Class"
                placeholder="Select class"
              />
            </div>

            <div className="mb-6">
              <label htmlFor="advanceBooking" className="block text-sm font-medium text-gray-600 mb-2">
                Advance Booking Requirement (Travel)
              </label>
              <Dropdown
                value={advanceBooking}
                onChange={setAdvanceBooking}
                options={advanceBookingOptions}
                label="Advance Booking"
                placeholder="Select requirement"
              />
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end">
          <Button
            type="submit"
            className="bg-teal-700 hover:bg-teal-800 px-6 py-3 rounded-md text-sm text-white"
          >
            Save Policy
          </Button>
        </div>
      </form>
    </div>
  );
};

export default AddPolicy;