import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { useState } from 'react';
import { FiSearch } from 'react-icons/fi';
import ManageIntegrationModal from './ManageIntegrationModal';

interface Integration {
  id: string;
  name: string;
  description: string;
  status: 'connected' | 'disconnected';
}

const Integrations = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null);
  const [integrations, setIntegrations] = useState<Integration[]>([
    {
      id: '1',
      name: 'Accounting System',
      description: 'Connect your accounting system to automate financial data synchronization.',
      status: 'connected',
    },
    {
      id: '2',
      name: 'HR Platform',
      description: 'Integrate your HR platform to streamline employee data management.',
      status: 'disconnected',
    },
    {
      id: '3',
      name: 'Payment Gateway',
      description: 'Enable payment processing through your preferred payment gateway.',
      status: 'connected',
    },
    {
      id: '4',
      name: 'CRM Tool',
      description: 'Sync customer data between your CRM and our platform.',
      status: 'disconnected',
    },
    {
      id: '5',
      name: 'Email Marketing Service',
      description: 'Connect your email marketing service to automate email campaigns.',
      status: 'connected',
    },
  ]);

  const filteredIntegrations = integrations.filter(integration =>
    integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    integration.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleConnect = (id: string) => {
    setIntegrations(prevIntegrations =>
      prevIntegrations.map(integration =>
        integration.id === id
          ? { ...integration, status: 'connected' as const }
          : integration
      )
    );
  };

  const handleManage = (id: string) => {
    const integration = integrations.find(item => item.id === id);
    if (integration) {
      setSelectedIntegration(integration);
      setIsModalOpen(true);
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Integrations</h1>
          <Breadcrumb pageName="Integrations" />
        </div>
      </div>
      
      {/* Search Bar */}
      <div className="relative mb-6">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <FiSearch className="w-5 h-5 text-gray-400" />
        </div>
        <input
          type="text"
          className="bg-gray-100 border border-gray-200 text-gray-700 text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full pl-10 p-2.5"
          placeholder="Search integrations..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Integrations Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-white">
            <tr>
              <th scope="col" className="px-6 py-4 text-left text-sm font-medium text-gray-500">
                Name
              </th>
              <th scope="col" className="px-6 py-4 text-left text-sm font-medium text-gray-500">
                Description
              </th>
              <th scope="col" className="px-6 py-4 text-left text-sm font-medium text-gray-500">
                Status
              </th>
              <th scope="col" className="px-6 py-4 text-left text-sm font-medium text-gray-500">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredIntegrations.map((integration) => (
              <tr key={integration.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {integration.name}
                </td>
                <td className="px-6 py-4 whitespace-normal text-sm text-gray-500 max-w-md">
                  {integration.description}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <span
                    className={`px-4 py-1 rounded-full text-sm ${
                      integration.status === 'connected'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {integration.status === 'connected' ? 'Connected' : 'Disconnected'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  {integration.status === 'connected' ? (
                    <button
                      onClick={() => handleManage(integration.id)}
                      className="text-gray-500 hover:text-teal-600 font-medium"
                    >
                      Manage
                    </button>
                  ) : (
                    <button
                      onClick={() => handleConnect(integration.id)}
                      className="text-gray-500 hover:text-teal-600 font-medium"
                    >
                      Connect
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Manage Integration Modal */}
      <ManageIntegrationModal 
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        integration={selectedIntegration}
      />
    </div>
  );
};

export default Integrations;
