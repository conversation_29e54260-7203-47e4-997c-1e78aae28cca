import React from 'react';
import { Search } from 'lucide-react';

interface NavbarProps {
  className?: string;
}

const Navbar: React.FC<NavbarProps> = ({ className = '' }) => {
  return (
    <nav className={`h-16 w-full px-4 md:px-6 py-10 flex items-center justify-between z-10 ${className}`}>
      <div className="flex items-center">
        <img src="/company-logo.png" alt="company-logo" className="h-16" />
      </div>
      <div className="flex items-center gap-5">
        <div className="flex gap-2 items-center w-80 md:w-96 border border-gray-200 rounded-full bg-gray-100 px-3 py-2 hover:bg-gray-50 focus-within:ring-2 focus-within:ring-teal-500 transition">
          <Search className="h-5 w-5 text-teal-500 mr-2" />
          <input
            type="text"
            placeholder="Search"
            className="flex-1 bg-transparent outline-none text-xs text-gray-500 font-semibold md:text-base"
          />
        </div>
        <img
          src='notificationIcon.svg'
          alt="notification"
          className="h-8 cursor-pointer"
        />
      </div>
    </nav>
  );
};

export default Navbar;
