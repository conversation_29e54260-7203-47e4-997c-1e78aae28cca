import React, { useState } from 'react';
import Modal from '../../../components/Modal';
import { Button } from '../../../components/Button';
import InputField from '../../../components/InputField';
import FormDropdown from '../../../components/FormDropdown';

interface AddUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (userData: UserFormData) => void;
}

interface UserFormData {
  firstName: string;
  lastName: string;
  email: string;
  manager: string;
  department: string;
  role: string;
  permissions: {
    viewExpenses: boolean;
    submitExpenses: boolean;
    editOwnExpenses: boolean;
    approveExpenses: boolean;
    viewAllExpenses: boolean;
    editAllExpenses: boolean;
    manageUsers: boolean;
    generateReports: boolean;
  };
}

const AddUserModal: React.FC<AddUserModalProps> = ({ isOpen, onClose, onSubmit }) => {
  const [formData, setFormData] = useState<UserFormData>({
    firstName: '',
    lastName: '',
    email: '',
    manager: '',
    department: '',
    role: '',
    permissions: {
      viewExpenses: false,
      submitExpenses: false,
      editOwnExpenses: false,
      approveExpenses: false,
      viewAllExpenses: false,
      editAllExpenses: false,
      manageUsers: false,
      generateReports: false,
    },
  });

  const managers = ['John Smith', 'Sarah Johnson', 'Mike Davis', 'Lisa Wilson'];
  const departments = ['Finance', 'HR', 'IT', 'Marketing', 'Sales'];
  const roles = ['Employee', 'Manager', 'Admin', 'Finance Manager'];

  const handleInputChange = (field: keyof UserFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePermissionChange = (permission: keyof UserFormData['permissions']) => {
    setFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [permission]: !prev.permissions[permission],
      },
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    onClose();

    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      manager: '',
      department: '',
      role: '',
      permissions: {
        viewExpenses: false,
        submitExpenses: false,
        editOwnExpenses: false,
        approveExpenses: false,
        viewAllExpenses: false,
        editAllExpenses: false,
        manageUsers: false,
        generateReports: false,
      },
    });
  };

  const basicPermissions: (keyof UserFormData['permissions'])[] = [
    'viewExpenses',
    'submitExpenses',
    'editOwnExpenses',
  ];

  const advancedPermissions: (keyof UserFormData['permissions'])[] = [
    'approveExpenses',
    'viewAllExpenses',
    'editAllExpenses',
    'manageUsers',
    'generateReports',
  ];

  const formatLabel = (key: string) =>
    key.replace(/([a-z])([A-Z])/g, '$1 $2').replace(/^./, s => s.toUpperCase());

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Add New User"
      className="max-w-4xl"
      disableBackdropClick
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <InputField
            size="sm"
            label="First Name"
            value={formData.firstName}
            onChange={value => handleInputChange('firstName', value)}
            placeholder="Enter first name"
            required
          />

          <FormDropdown
            size="sm"
            value={formData.manager}
            onChange={value => handleInputChange('manager', value)}
            options={managers}
            label="Manager"
            placeholder="Select manager"
            required
          />

          <InputField
            size="sm"
            label="Last Name"
            value={formData.lastName}
            onChange={value => handleInputChange('lastName', value)}
            placeholder="Enter last name"
            required
          />

          <FormDropdown
            size="sm"
            value={formData.department}
            onChange={value => handleInputChange('department', value)}
            options={departments}
            label="Department"
            placeholder="Select department"
            required
          />

          <InputField
            size="sm"
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={value => handleInputChange('email', value)}
            placeholder="Enter email address"
            required
          />

          <FormDropdown
            size="sm"
            value={formData.role}
            onChange={value => handleInputChange('role', value)}
            options={roles}
            label="Role"
            placeholder="Select role"
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 pt-2">
          <div>
            <div className="text-md font-bold text-gray-500 mb-5">Role Permissions</div>
            <p className="text-sm text-gray-900 mb-8">
              Default permissions for selected role will be applied. You can customize permissions below.
            </p>
            <div className="space-y-3">
              {basicPermissions.map((perm) => (
                <label key={perm} className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.permissions[perm]}
                    onChange={() => handlePermissionChange(perm)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{formatLabel(perm)}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-md font-bold text-gray-500 mb-8">Customize Permissions (Optional)</h3>
            <div className="space-y-3">
              {advancedPermissions.map((perm) => (
                <label key={perm} className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.permissions[perm]}
                    onChange={() => handlePermissionChange(perm)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{formatLabel(perm)}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-6">
          <Button
            type="submit"
            className="brand-gradient text-white px-24 py-3 rounded-md font-medium"
            onClick={() => alert("Submitting...")}
          >
            Submit
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default AddUserModal;
