import { useState, useRef } from "react";
import { ChevronDown } from "lucide-react";

interface DropdownProps {
  value: string;
  onChange: (value: string) => void;
  options: string[];
  label: string;
  className?: string;
  placeholder?: string;
  fullWidth?: boolean;
}

export default function Dropdown({
  value,
  onChange,
  options,
  label,
  className = "",
  placeholder,
  fullWidth = false,
}: DropdownProps) {
  const [open, setOpen] = useState(false);
  const displayLabel = value || placeholder || `Select ${label.toLowerCase()}`;

  const handleSelect = (opt: string) => {
    onChange(opt);
    setOpen(false);
  };

  return (
    <div
      tabIndex={0}
      onBlur={(e) => {
        if (!e.currentTarget.contains(e.relatedTarget)) {
          setOpen(false);
        }
      }}
      className={`relative inline-block focus:outline-none ${fullWidth ? "w-full" : "w-fit"} ${className}`}
    >
      <button
        type="button"
        onClick={() => setOpen((prev) => !prev)}
        className={`cursor-pointer flex items-center justify-between gap-8 px-6 py-3 rounded-full bg-white shadow-sm transition text-sm w-full ${
          value ? "text-gray-700" : "text-gray-500"
        }`}
      >
        <span>{displayLabel}</span>
        <ChevronDown
          className={`w-4 h-4 transition-transform duration-200 ${open ? "rotate-180" : ""}`}
        />
      </button>

      {open && (
        <div className="absolute z-20 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg p-1 max-h-60 overflow-y-auto w-full">
          {options.map((opt) => (
            <div
              key={opt}
              onClick={() => handleSelect(opt)}
              className={`cursor-pointer px-4 py-2 rounded-lg text-sm text-gray-800 hover:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-700 ${
                value === opt ? "bg-gray-100 dark:bg-neutral-700" : ""
              }`}
            >
              {opt}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
