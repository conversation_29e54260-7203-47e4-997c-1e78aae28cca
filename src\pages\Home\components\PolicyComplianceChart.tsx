import React from 'react';
import {
  Pie<PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
} from 'recharts';

interface PolicyComplianceChartProps {
  percentage: number;
}

const PolicyComplianceChart: React.FC<PolicyComplianceChartProps> = ({
  percentage,
}) => {
  const remainingPercentage = 100 - percentage;

  const pieData = [
    {
      name: 'Within Policy',
      value: percentage,
      color: '#00be5c',
    },
    {
      name: 'Outside Policy',
      value: remainingPercentage,
      color: '#5c738a',
    },
  ];

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 min-h-[400px] h-full flex flex-col">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Policy Compliance
      </h3>

      <div className="flex-1 flex items-center justify-center">
        <div className="relative w-full h-full" style={{ minHeight: "280px" }}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                innerRadius="70%"
                outerRadius="85%"
                startAngle={90}
                endAngle={450}
                dataKey="value"
                strokeWidth={5}
                stroke="#ffffff"
                cornerRadius={10}
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>

          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <div className="text-4xl font-bold text-gray-900">
              {percentage}%
            </div>
            <div className="text-sm text-gray-600 text-center">
              Within Policy
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PolicyComplianceChart;
