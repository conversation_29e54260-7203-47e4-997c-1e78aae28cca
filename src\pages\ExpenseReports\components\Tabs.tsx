interface TabsProps {
  activeTab: string;
  onChange: (tab: 'all' | 'approvals' | 'disputes') => void;
}

const Tabs = ({ activeTab, onChange }: TabsProps) => {
  const tabs: { key: 'all' | 'approvals' | 'disputes'; label: string }[] = [
    { key: 'all', label: 'All Expenses' },
    { key: 'approvals', label: 'Approvals' },
    { key: 'disputes', label: 'Disputes' },
  ];

  return (
    <div className="mb-6 w-full">
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-8 py-4 px-8 bg-white border border-gray-200 rounded-xl shadow-sm">
        {tabs.map((tab) => {
          const isActive = activeTab === tab.key;

          return (
            <button
              key={tab.key}
              onClick={() => onChange(tab.key)}
              className={`cursor-pointer w-full text-sm sm:text-base px-4 py-3 rounded-md font-medium text-center transition-all duration-200 ease-in-out focus:outline-none ${
                isActive
                  ? 'bg-teal-600 text-white shadow-md ring-2'
                  : 'bg-gray-100 text-gray-700 hover:bg-teal-50 hover:text-teal-700 hover:shadow-sm hover:ring-teal-200'
              }`}
            >
              {tab.label}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default Tabs;
