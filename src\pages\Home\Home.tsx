import React from 'react';
import StatCard from './components/StatCard';
import ExpenseTrendChart from './components/ExpenseTrendChart';
import PolicyComplianceChart from './components/PolicyComplianceChart';
import ActivityList from './components/ActivityList';
import UserActivityFeed from './components/UserActivityFeed';
import { formatCurrency, formatNumber } from '../../utils/utils';
import { dashboardStats, policyCompliance } from '../../mockData/homeMockData';

const Home: React.FC = () => {
  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <StatCard
          title="Total Expenses Claimed"
          value={formatCurrency(dashboardStats.totalExpensesClaimed)}
          change={dashboardStats.expensesClaimedChange}
        />
        <StatCard
          title="Total Reimbursements Paid"
          value={formatCurrency(dashboardStats.totalReimbursementsPaid)}
          change={dashboardStats.reimbursementsPaidChange}
        />
        <StatCard
          title="Pending Requests"
          value={formatNumber(dashboardStats.pendingRequests)}
          change={dashboardStats.pendingRequestsChange}
        />
        <StatCard
          title="Average Reimburse Time"
          value={`${dashboardStats.averageReimburseTime} days`}
          change={dashboardStats.averageTimeChange}
          isTime={true}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 mb-6 items-stretch">
        <div className="lg:col-span-9">
          <ExpenseTrendChart />
        </div>
        <div className="lg:col-span-3">
          <PolicyComplianceChart percentage={policyCompliance.percentage} />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 items-stretch">
        <div className="lg:col-span-8 h-full flex flex-col">
          <ActivityList />
        </div>
        <div className="lg:col-span-4 h-full flex flex-col">
          <UserActivityFeed />
        </div>
      </div>
    </div>
  );
};

export default Home;
