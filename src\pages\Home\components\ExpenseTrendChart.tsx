import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Toolt<PERSON>,
  ResponsiveContainer,
} from 'recharts';
import { FaRegCalendar, FaCaretUp, FaCheck } from 'react-icons/fa';
import { RiBarChartFill } from 'react-icons/ri';

const expenseTrendData = [
  { month: 'SEP', value: 115, budget: 100 },
  { month: 'OCT', value: 95, budget: 100 },
  { month: 'NOV', value: 165, budget: 120 },
  { month: 'DEC', value: 95, budget: 100 },
  { month: 'JAN', value: 110, budget: 105 },
  { month: 'FEB', value: 115, budget: 110 },
];

const ExpenseTrendChart: React.FC = () => {
  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 min-h-[400px] h-full flex flex-col">

      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-12 gap-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">User Activity</h3>

        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-12">

          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-8">
            <div className="flex items-center gap-2">
              <span className="text-xs text-slate-500 font-medium">This Month</span>
              <div className="w-6 h-1 bg-[#08ccb5] rounded-full" />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-slate-500 font-medium">Past Month</span>
              <div className="w-6 h-1 bg-[#009191] rounded-full" />
            </div>
          </div>

          <div className="flex items-center gap-2 sm:gap-3">
            <button
              className="px-3 sm:px-5 py-2 sm:py-2.5 rounded-lg text-xs text-[slategray] flex items-center gap-2 sm:gap-3 bg-blue-50"
              onClick={() => { }}
            >
              <FaRegCalendar className="w-3 h-3 sm:w-4 sm:h-4 text-[slategray]" />
              <span className="hidden sm:inline">This Year</span>
              <span className="sm:hidden">Year</span>
            </button>
            <button
              className="p-2 rounded-lg text-gray-500 bg-blue-50"
              title="View chart"
              onClick={() => { }}
            >
              <RiBarChartFill className="w-3 h-3 sm:w-4 sm:h-4 text-teal-600" />
            </button>
          </div>
        </div>
      </div>

      <div className="flex h-48 sm:h-60 gap-4">

        <div className="flex flex-col justify-between w-40 sm:w-52">
          <div>
            <div className="text-2xl sm:text-3xl font-bold text-[slategray]">$37.5K</div>
            <div className="flex items-center gap-6 mt-2">
              <div className="text-sm text-[slategray] font-semibold">Total Spent</div>
              <div className="text-sm text-green-500 font-bold flex items-center gap-1">
                <FaCaretUp />
                <span>+2.45%</span>
              </div>
            </div>

            <div className="flex items-center gap-2 mt-8">
              <div className="w-4 h-4 bg-green-400 p-1 rounded-full flex items-center justify-center">
                <FaCheck className="text-white w-3 h-3" />
              </div>
              <span className="text-sm font-bold text-green-400">On track</span>
            </div>
          </div>
        </div>

        <div className="flex-1">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={expenseTrendData}
              margin={{ top: 5, right: 10, left: 25, bottom: 20 }}
            >
              <XAxis
                dataKey="month"
                axisLine={false}
                tickLine={false}
                tick={{
                  fontSize: 12,
                  fill: '#9CA3AF',
                  fontWeight: 'bold',
                }}
                dy={10}
              />
              <YAxis hide />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#08ccb5"
                strokeWidth={4}
                dot={false}
                name="This Month"
                activeDot={{
                  r: 6,
                  fill: 'white',
                  stroke: '#08ccb5',
                  strokeWidth: 3,
                }}
              />
              <Line
                type="monotone"
                dataKey="budget"
                stroke="#009191"
                strokeWidth={4}
                dot={false}
                name="Past Month"
                activeDot={{
                  r: 6,
                  fill: 'white',
                  stroke: '#009191',
                  strokeWidth: 3,
                }}
              />
              <Tooltip
                content={({ active, payload }) => {
                  if (active && payload && payload.length === 2) {
                    return (
                      <div className="bg-teal-500 text-white p-2 rounded-lg text-xs font-medium shadow-lg">
                        <div className="flex flex-col gap-1">
                          <div>This Month: ${payload[0].value}.00</div>
                          <div>Past Month: ${payload[1].value}.00</div>
                        </div>
                      </div>
                    );
                  }
                  return null;
                }}
                cursor={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default ExpenseTrendChart;
