import React from 'react'

const ApprovalTable = () => {
  return (
              <div>
                <div className="flex flex-wrap gap-4 mb-6">
                  <div className="flex items-center gap-2">
                    <FiCalendar className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">Date Range</span>
                  </div>
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Manager', 'Senior Manager', 'Team Lead']}
                    label="Designation"
                    className="min-w-[120px]"
                  />
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Marketing', 'Sales', 'Engineering', 'Product Design']}
                    label="Department"
                    className="min-w-[120px]"
                  />
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Pending', 'Approved', 'Rejected']}
                    label="Status"
                    className="min-w-[120px]"
                  />
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Manager Approved', 'Finance Approved', 'Pending']}
                    label="Approval Status"
                    className="min-w-[140px]"
                  />
                </div>

                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Employee Name</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Department</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Designation</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Expense Category</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Submission Date</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Total Amount</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedData.length > 0 ? (
                      paginatedData.map((approval) => {
                        const approvalItem = approval as ApprovalData;
                        return (
                          <tr key={approvalItem.id} className="border-b border-gray-100 hover:bg-gray-50">
                            <td className="py-4 px-4 text-sm text-gray-900">{approvalItem.employeeName}</td>
                            <td className="py-4 px-4 text-sm text-gray-600">{approvalItem.department}</td>
                            <td className="py-4 px-4 text-sm text-gray-600">{approvalItem.designation}</td>
                            <td className="py-4 px-4 text-sm text-gray-600 flex items-center gap-2">
                              <span className="w-6 h-6 bg-gray-800 rounded flex items-center justify-center">
                                <img src='categoryIcon.svg' alt="Category Icon" />
                              </span>
                              {approvalItem.expenseCategory}
                            </td>
                            <td className="py-4 px-4 text-sm text-gray-600">{approvalItem.submissionDate}</td>
                            <td className="py-4 px-4 text-sm text-gray-900 font-medium">
                              ${approvalItem.totalAmount.toFixed(2)}
                            </td>
                            <td className="py-4 px-4">
                              <button className="bg-blue-100 text-blue-700 px-4 py-2 rounded text-sm hover:bg-blue-200">
                                View
                              </button>
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={7} className="py-8 px-4 text-center text-gray-500">
                          No approvals found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
  )
}

export default ApprovalTable
