import { useState } from 'react';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { Search } from 'lucide-react';

interface AuditLogEntry {
  timestamp: string;
  timeDetail: string;
  user: string;
  actionType: string;
  objectItem: string;
  description: string;
  ipAddress: string;
  status: 'Success' | 'Failure';
}

const AuditTrail = () => {
  const [searchQuery, setSearchQuery] = useState('');
  
  // Mock data for audit logs
  const auditLogs: AuditLogEntry[] = [
    {
      timestamp: '07/26/2024',
      timeDetail: '14:30:00',
      user: '<PERSON>',
      actionType: 'User Login',
      objectItem: 'N/A',
      description: 'Successful login',
      ipAddress: '*************',
      status: 'Success'
    },
    {
      timestamp: '07/26/2024',
      timeDetail: '14:45:15',
      user: '<PERSON>',
      actionType: 'Report Generated',
      objectItem: 'Report #12345',
      description: 'Generated sales report for Q2',
      ipAddress: '*************',
      status: 'Success'
    },
    {
      timestamp: '07/26/2024',
      timeDetail: '15:00:20',
      user: '<PERSON>',
      actionType: 'User Logout',
      objectItem: 'N/A',
      description: 'User logged out',
      ipAddress: '*************',
      status: 'Success'
    },
    {
      timestamp: '07/26/2024',
      timeDetail: '15:15:30',
      user: 'Liam Carter',
      actionType: 'Data Update',
      objectItem: 'Customer #67890',
      description: 'Changed: Max Amount from $50 to $60',
      ipAddress: '*************',
      status: 'Success'
    },
    {
      timestamp: '07/26/2024',
      timeDetail: '15:30:45',
      user: 'Sophia Evans',
      actionType: 'System Error',
      objectItem: 'N/A',
      description: 'Failed to process payment transaction',
      ipAddress: '*************',
      status: 'Failure'
    }
  ];

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Audit Trail</h1>
          <Breadcrumb pageName="Audit Trail" />
        </div>
      </div>
      
      {/* Search Bar */}
      <div className="relative mb-6">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <Search className="w-5 h-5 text-gray-400" />
        </div>
        <input
          type="text"
          className="bg-gray-100 border border-gray-200 text-gray-600 text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full pl-10 p-2.5"
          placeholder="Search audit logs..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      
      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="relative">
          <select className="bg-white border border-gray-200 text-gray-600 text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2.5 appearance-none">
            <option value="">Action/Event Type</option>
            <option value="login">User Login</option>
            <option value="logout">User Logout</option>
            <option value="report">Report Generated</option>
            <option value="update">Data Update</option>
            <option value="error">System Error</option>
          </select>
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
            </svg>
          </div>
        </div>
        
        <div className="relative">
          <select className="bg-white border border-gray-200 text-gray-600 text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2.5 appearance-none">
            <option value="">Department</option>
            <option value="finance">Finance</option>
            <option value="hr">HR</option>
            <option value="it">IT</option>
            <option value="sales">Sales</option>
          </select>
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
            </svg>
          </div>
        </div>
        
        <div className="relative">
          <select className="bg-white border border-gray-200 text-gray-600 text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2.5 appearance-none">
            <option value="">Role</option>
            <option value="admin">Admin</option>
            <option value="manager">Manager</option>
            <option value="user">User</option>
          </select>
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
            </svg>
          </div>
        </div>
        
        <div className="relative">
          <select className="bg-white border border-gray-200 text-gray-600 text-sm rounded-lg focus:ring-teal-500 focus:border-teal-500 block w-full p-2.5 appearance-none">
            <option value="">Approval Status</option>
            <option value="approved">Approved</option>
            <option value="pending">Pending</option>
            <option value="rejected">Rejected</option>
          </select>
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
            </svg>
          </div>
        </div>
      </div>
      
      {/* Audit Logs Table */}
      <div className="overflow-x-auto bg-white rounded-lg shadow">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-white">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Timestamp
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Action/Event Type
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Object/Item
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Details/Description
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                IP Address
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {auditLogs.map((log, index) => (
              <tr key={index}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{log.timestamp}</div>
                  <div className="text-sm text-gray-500">{log.timeDetail}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{log.user}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{log.actionType}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{log.objectItem}</div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-900">{log.description}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{log.ipAddress}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    log.status === 'Success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {log.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AuditTrail;