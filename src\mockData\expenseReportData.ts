export interface EmployeeExpenseReportData {
  employeeDetails: {
    name: string;
    employeeId: string;
    department: string;
    reportPeriod: string;
  };
  keySummary: {
    totalExpenses: number;
    totalReimbursement: number;
    outOfPocket: number;
  };
  reportDetail: {
    reportNumber: string;
    submittedOn: string;
    approvedOn: string;
    status: string;
  };
  detailedExpenseTable: {
    date: string;
    expenseName: string;
    category: string;
    amount: number;
    status: string;
    notes: string;
  }[];
  categoryBreakdown: {
    name: string;
    amount: number;
  }[];
  disputesAndExceptions: {
    date: string;
    expense: string;
    amount: number;
    disputeStatus: string;
    description: string;
  }[];
  approvalLog: {
    action: string;
    approver: string;
    date: string;
    comments: string;
  }[];
}

export const employeeExpenseReportData: EmployeeExpenseReportData = {
  employeeDetails: {
    name: "<PERSON> Do<PERSON>",
    employeeId: "EMP-001",
    department: "Marketing",
    reportPeriod: "Jan 01 - Jan 31, 2024"
  },
  keySummary: {
    totalExpenses: 4320.00,
    totalReimbursement: 4320.00,
    outOfPocket: 0.00
  },
  reportDetail: {
    reportNumber: "RPT-2024-001",
    submittedOn: "Feb 01, 2024",
    approvedOn: "Feb 05, 2024",
    status: "Approved"
  },
  detailedExpenseTable: [
    {
      date: "Jan 15, 2024",
      expenseName: "Client Dinner",
      category: "Meals & Entertainment",
      amount: 150.00,
      status: "Approved",
      notes: "Dinner with client"
    },
    {
      date: "Jan 18, 2024",
      expenseName: "Travel & Conference",
      category: "Travel & Entertainment",
      amount: 1200.00,
      status: "Approved",
      notes: "Conference attendance"
    },
    {
      date: "Jan 20, 2024",
      expenseName: "Hotel Stay",
      category: "Travel",
      amount: 450.00,
      status: "Approved",
      notes: "Hotel for conference"
    },
    {
      date: "Jan 22, 2024",
      expenseName: "Office Supplies",
      category: "Office Expenses",
      amount: 75.00,
      status: "Approved",
      notes: "Stationery and supplies"
    },
    {
      date: "Jan 25, 2024",
      expenseName: "Transportation",
      category: "Travel",
      amount: 120.00,
      status: "Approved",
      notes: "Taxi and metro"
    },
    {
      date: "Jan 28, 2024",
      expenseName: "Software License",
      category: "Technology",
      amount: 299.00,
      status: "Approved",
      notes: "Annual software license"
    },
    {
      date: "Jan 30, 2024",
      expenseName: "Team Lunch",
      category: "Meals & Entertainment",
      amount: 180.00,
      status: "Approved",
      notes: "Team building lunch"
    },
    {
      date: "Jan 31, 2024",
      expenseName: "Parking Fees",
      category: "Travel",
      amount: 45.00,
      status: "Approved",
      notes: "Monthly parking"
    }
  ],
  categoryBreakdown: [
    { name: "Travel & Transportation", amount: 1815.00 },
    { name: "Meals & Entertainment", amount: 330.00 },
    { name: "Technology", amount: 299.00 },
    { name: "Office Expenses", amount: 75.00 },
    { name: "Other Expenses", amount: 801.00 },
    { name: "Miscellaneous", amount: 0.00 }
  ],
  disputesAndExceptions: [
    {
      date: "Jan 15, 2024",
      expense: "Client Dinner",
      amount: 150.00,
      disputeStatus: "Resolved",
      description: "Initial dispute over meal cost resolved"
    }
  ],
  approvalLog: [
    {
      action: "Manager approved 'Client Dinner'",
      approver: "Sarah Johnson",
      date: "Feb 02, 2024",
      comments: "Approved after review"
    },
    {
      action: "Manager approved 'Travel & Conference'",
      approver: "Sarah Johnson", 
      date: "Feb 02, 2024",
      comments: "Pre-approved conference expense"
    },
    {
      action: "Manager approved 'Hotel Stay'",
      approver: "Sarah Johnson",
      date: "Feb 02, 2024",
      comments: "Conference accommodation approved"
    },
    {
      action: "Manager approved 'Office Supplies'",
      approver: "Sarah Johnson",
      date: "Feb 03, 2024",
      comments: "Standard office supplies"
    },
    {
      action: "Manager approved 'Transportation'",
      approver: "Sarah Johnson",
      date: "Feb 03, 2024",
      comments: "Business travel expenses"
    },
    {
      action: "Manager approved 'Software License'",
      approver: "Sarah Johnson",
      date: "Feb 04, 2024",
      comments: "Required for work"
    },
    {
      action: "Manager approved 'Team Lunch'",
      approver: "Sarah Johnson",
      date: "Feb 04, 2024",
      comments: "Team building activity"
    },
    {
      action: "Manager approved 'Parking Fees'",
      approver: "Sarah Johnson",
      date: "Feb 05, 2024",
      comments: "Monthly parking approved"
    }
  ]
};
