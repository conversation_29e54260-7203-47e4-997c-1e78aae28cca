interface User {
    id: string;
    name: string;
    email: string;
    department: string;
    role: string;
    status: 'Active' | 'Inactive';
    lastLogin: string;
    avatar?: string;
}

export const dummyUserTableData: User[] = [
    {
        id: "1",
        name: "<PERSON>",
        email: "<EMAIL>",
        department: "HR",
        role: "Manager",
        status: "Active",
        lastLogin: "2025-07-14",
    },
    {
        id: "2",
        name: "<PERSON>",
        email: "<EMAIL>",
        department: "Finance",
        role: "Analyst",
        status: "Inactive",
        lastLogin: "2025-06-29",
    },
    {
        id: "3",
        name: "<PERSON>",
        email: "<EMAIL>",
        department: "B<PERSON>",
        role: "Specialist",
        status: "Active",
        lastLogin: "2025-07-10",
    },
    {
        id: "4",
        name: "<PERSON>",
        email: "<EMAIL>",
        department: "HR",
        role: "Analyst",
        status: "Inactive",
        lastLogin: "2025-05-20",
    },
    {
        id: "5",
        name: "<PERSON>",
        email: "<EMAIL>",
        department: "Finance",
        role: "Manager",
        status: "Active",
        lastLogin: "2025-07-16",
    },
    {
        id: "6",
        name: "<PERSON> White",
        email: "<EMAIL>",
        department: "BI",
        role: "Specialist",
        status: "Active",
        lastLogin: "2025-07-15",
    },
    {
        id: "7",
        name: "George <PERSON>",
        email: "<EMAIL>",
        department: "HR",
        role: "Manager",
        status: "Inactive",
        lastLogin: "2025-06-01",
    },
    {
        id: "8",
        name: "Hannah Green",
        email: "<EMAIL>",
        department: "Finance",
        role: "Analyst",
        status: "Active",
        lastLogin: "2025-07-11",
    },
    {
        id: "9",
        name: "Isaac Lee",
        email: "<EMAIL>",
        department: "BI",
        role: "Specialist",
        status: "Inactive",
        lastLogin: "2025-07-03",
    },
    {
        id: "10",
        name: "Jenna Patel",
        email: "<EMAIL>",
        department: "HR",
        role: "Analyst",
        status: "Active",
        lastLogin: "2025-07-13",
    },
];

export const userRoleData = [
  { role: 'Manager', count: 32 },
  { role: 'Finance', count: 12 },
  { role: 'Analyst', count: 8 },
  { role: 'Lead', count: 18 },
  { role: 'Specialist', count: 28 },
  { role: 'Coordinator', count: 38 },
  { role: 'Designer', count: 25 }
];

export interface ExpenseData {
  id: string;
  employeeName: string;
  department: string;
  submissionDate: string;
  totalAmount: number;
  approvalStatus: 'Manager Approved' | 'Finance Approved' | 'Manager Rejected' | 'Pending';
  paymentMethod: string;
}

export interface ApprovalData {
  id: string;
  employeeName: string;
  department: string;
  designation: string;
  expenseCategory: string;
  submissionDate: string;
  totalAmount: number;
}

export interface DisputeData {
  id: string;
  name: string;
  department: string;
  expenseType: string;
  disputeRaisedOn: string;
  disputeAmount: number;
  rejectedBy: string;
  currentStatus: 'Pending' | 'Approved' | 'In review';
}

export const statsData = [
  {
    title: 'Total Expenses (Period)',
    value: '$12,500.00',
    change: '+10%',
    changeType: 'positive' as const
  },
  {
    title: 'Average Expense Amount',
    value: '$250.00',
    change: '+8%',
    changeType: 'positive' as const
  },
  {
    title: 'Reports Pending Approval',
    value: '23',
    change: '-5%',
    changeType: 'negative' as const
  },
  {
    title: 'Average Approval Time',
    value: '2 days, 4 hours',
    change: '-2 Days',
    changeType: 'negative' as const
  }
];

export const expenseData: ExpenseData[] = [
  {
    id: '1',
    employeeName: 'Ethan Harper',
    department: 'Marketing',
    submissionDate: '2024-07-26',
    totalAmount: 250.0,
    approvalStatus: 'Manager Approved',
    paymentMethod: 'Direct Deposit',
  },
  {
    id: '2',
    employeeName: 'Olivia Bennett',
    department: 'Sales',
    submissionDate: '2024-07-25',
    totalAmount: 180.0,
    approvalStatus: 'Finance Approved',
    paymentMethod: 'Direct Deposit',
  },
  {
    id: '3',
    employeeName: 'Noah Carter',
    department: 'Engineering',
    submissionDate: '2024-07-24',
    totalAmount: 320.0,
    approvalStatus: 'Manager Rejected',
    paymentMethod: 'Direct Deposit',
  },
  {
    id: '4',
    employeeName: 'Ava Thompson',
    department: 'Product',
    submissionDate: '2024-07-23',
    totalAmount: 450.0,
    approvalStatus: 'Pending',
    paymentMethod: '-',
  },
  {
    id: '5',
    employeeName: 'Liam Foster',
    department: 'HR',
    submissionDate: '2024-07-22',
    totalAmount: 120.0,
    approvalStatus: 'Finance Approved',
    paymentMethod: 'Direct Deposit',
  },
  {
    id: '6',
    employeeName: 'Sophia Reed',
    department: 'Finance',
    submissionDate: '2024-07-21',
    totalAmount: 290.0,
    approvalStatus: 'Pending',
    paymentMethod: 'Direct Deposit',
  },
  {
    id: '7',
    employeeName: 'Benjamin Hayes',
    department: 'Marketing',
    submissionDate: '2024-07-20',
    totalAmount: 340.0,
    approvalStatus: 'Manager Approved',
    paymentMethod: 'Cheque',
  },
  {
    id: '8',
    employeeName: 'Emma Collins',
    department: 'Sales',
    submissionDate: '2024-07-19',
    totalAmount: 215.0,
    approvalStatus: 'Manager Approved',
    paymentMethod: 'Direct Deposit',
  },
  {
    id: '9',
    employeeName: 'William Brooks',
    department: 'Engineering',
    submissionDate: '2024-07-18',
    totalAmount: 385.0,
    approvalStatus: 'Finance Approved',
    paymentMethod: '-',
  },
  {
    id: '10',
    employeeName: 'Mia Turner',
    department: 'HR',
    submissionDate: '2024-07-17',
    totalAmount: 165.0,
    approvalStatus: 'Manager Rejected',
    paymentMethod: 'Direct Deposit',
  },
  {
    id: '11',
    employeeName: 'James Wilson',
    department: 'Marketing',
    submissionDate: '2024-07-16',
    totalAmount: 275.0,
    approvalStatus: 'Manager Approved',
    paymentMethod: 'Direct Deposit',
  },
  {
    id: '12',
    employeeName: 'Sarah Johnson',
    department: 'Sales',
    submissionDate: '2024-07-15',
    totalAmount: 195.0,
    approvalStatus: 'Finance Approved',
    paymentMethod: 'Direct Deposit',
  },
  {
    id: '13',
    employeeName: 'Michael Brown',
    department: 'Engineering',
    submissionDate: '2024-07-14',
    totalAmount: 420.0,
    approvalStatus: 'Pending',
    paymentMethod: '-',
  },
  {
    id: '14',
    employeeName: 'Lisa Davis',
    department: 'Product',
    submissionDate: '2024-07-13',
    totalAmount: 310.0,
    approvalStatus: 'Manager Rejected',
    paymentMethod: 'Direct Deposit',
  },
  {
    id: '15',
    employeeName: 'David Miller',
    department: 'HR',
    submissionDate: '2024-07-12',
    totalAmount: 155.0,
    approvalStatus: 'Finance Approved',
    paymentMethod: 'Direct Deposit',
  },
  {
    id: '16',
    employeeName: 'Rachel Green',
    department: 'Marketing',
    submissionDate: '2024-07-11',
    totalAmount: 225.0,
    approvalStatus: 'Manager Approved',
    paymentMethod: 'Direct Deposit',
  },
  {
    id: '17',
    employeeName: 'Tom Wilson',
    department: 'Sales',
    submissionDate: '2024-07-10',
    totalAmount: 190.0,
    approvalStatus: 'Pending',
    paymentMethod: '-',
  },
  {
    id: '18',
    employeeName: 'Anna Davis',
    department: 'Engineering',
    submissionDate: '2024-07-09',
    totalAmount: 380.0,
    approvalStatus: 'Finance Approved',
    paymentMethod: 'Direct Deposit',
  },
];

export const approvalData: ApprovalData[] = [
  {
    id: '1',
    employeeName: 'Ethan Harper',
    department: 'Marketing',
    designation: 'Manager',
    expenseCategory: 'Multiple Expenses',
    submissionDate: '2024-07-26',
    totalAmount: 250.0,
  },
  {
    id: '2',
    employeeName: 'Lila Brooks',
    department: 'Product Design',
    designation: 'Manager',
    expenseCategory: 'Multiple Expenses',
    submissionDate: '2024-07-26',
    totalAmount: 450.0,
  },
  {
    id: '3',
    employeeName: 'James Wilson',
    department: 'Marketing',
    designation: 'Senior Analyst',
    expenseCategory: 'Travel Expenses',
    submissionDate: '2024-07-25',
    totalAmount: 320.0,
  },
  {
    id: '4',
    employeeName: 'Sarah Johnson',
    department: 'Sales',
    designation: 'Team Lead',
    expenseCategory: 'Client Meeting',
    submissionDate: '2024-07-24',
    totalAmount: 195.0,
  },
  {
    id: '5',
    employeeName: 'Michael Brown',
    department: 'Engineering',
    designation: 'Senior Developer',
    expenseCategory: 'Conference Expenses',
    submissionDate: '2024-07-23',
    totalAmount: 420.0,
  },
  {
    id: '6',
    employeeName: 'Lisa Davis',
    department: 'Product',
    designation: 'Product Manager',
    expenseCategory: 'Research Expenses',
    submissionDate: '2024-07-22',
    totalAmount: 310.0,
  },
  {
    id: '7',
    employeeName: 'David Miller',
    department: 'HR',
    designation: 'HR Specialist',
    expenseCategory: 'Training Expenses',
    submissionDate: '2024-07-21',
    totalAmount: 155.0,
  },
  {
    id: '8',
    employeeName: 'Emma Collins',
    department: 'Sales',
    designation: 'Sales Representative',
    expenseCategory: 'Travel Expenses',
    submissionDate: '2024-07-20',
    totalAmount: 215.0,
  },
  {
    id: '9',
    employeeName: 'William Brooks',
    department: 'Engineering',
    designation: 'Tech Lead',
    expenseCategory: 'Equipment Purchase',
    submissionDate: '2024-07-19',
    totalAmount: 385.0,
  },
  {
    id: '10',
    employeeName: 'Sophia Reed',
    department: 'Finance',
    designation: 'Financial Analyst',
    expenseCategory: 'Office Supplies',
    submissionDate: '2024-07-18',
    totalAmount: 290.0,
  },
  {
    id: '11',
    employeeName: 'Benjamin Hayes',
    department: 'Marketing',
    designation: 'Marketing Coordinator',
    expenseCategory: 'Advertising Expenses',
    submissionDate: '2024-07-17',
    totalAmount: 340.0,
  },
  {
    id: '12',
    employeeName: 'Olivia Bennett',
    department: 'Sales',
    designation: 'Account Manager',
    expenseCategory: 'Client Entertainment',
    submissionDate: '2024-07-16',
    totalAmount: 180.0,
  },
  {
    id: '13',
    employeeName: 'Rachel Green',
    department: 'Marketing',
    designation: 'Marketing Manager',
    expenseCategory: 'Campaign Expenses',
    submissionDate: '2024-07-15',
    totalAmount: 225.0,
  },
  {
    id: '14',
    employeeName: 'Tom Wilson',
    department: 'Sales',
    designation: 'Sales Manager',
    expenseCategory: 'Travel Expenses',
    submissionDate: '2024-07-14',
    totalAmount: 190.0,
  },
  {
    id: '15',
    employeeName: 'Anna Davis',
    department: 'Engineering',
    designation: 'Senior Engineer',
    expenseCategory: 'Technical Conference',
    submissionDate: '2024-07-13',
    totalAmount: 380.0,
  },
];

export const disputeData: DisputeData[] = [
  {
    id: '1',
    name: 'Ethan Harper',
    department: 'Sales',
    expenseType: 'Multiple Expenses',
    disputeRaisedOn: '2024-07-25',
    disputeAmount: 180.0,
    rejectedBy: 'Manager',
    currentStatus: 'Pending',
  },
  {
    id: '2',
    name: 'Sofia Martinez',
    department: 'Marketing',
    expenseType: 'Conference Expenses',
    disputeRaisedOn: '2024-06-12',
    disputeAmount: 250.0,
    rejectedBy: 'Manager',
    currentStatus: 'Approved',
  },
  {
    id: '3',
    name: 'Liam Johnson',
    department: 'Development',
    expenseType: 'Software Licenses',
    disputeRaisedOn: '2024-08-05',
    disputeAmount: 1200.0,
    rejectedBy: 'Finance',
    currentStatus: 'Pending',
  },
  {
    id: '4',
    name: 'Ava Thompson',
    department: 'Human Resources',
    expenseType: 'Recruitment Costs',
    disputeRaisedOn: '2024-09-15',
    disputeAmount: 800.0,
    rejectedBy: 'Finance',
    currentStatus: 'In review',
  },
  {
    id: '5',
    name: 'Noah Carter',
    department: 'Engineering',
    expenseType: 'Travel Expenses',
    disputeRaisedOn: '2024-07-20',
    disputeAmount: 320.0,
    rejectedBy: 'Manager',
    currentStatus: 'Pending',
  },
  {
    id: '6',
    name: 'Mia Turner',
    department: 'HR',
    expenseType: 'Training Expenses',
    disputeRaisedOn: '2024-07-15',
    disputeAmount: 165.0,
    rejectedBy: 'Manager',
    currentStatus: 'In review',
  },
  {
    id: '7',
    name: 'Lisa Davis',
    department: 'Product',
    expenseType: 'Research Expenses',
    disputeRaisedOn: '2024-07-10',
    disputeAmount: 310.0,
    rejectedBy: 'Finance',
    currentStatus: 'Approved',
  },
  {
    id: '8',
    name: 'James Wilson',
    department: 'Marketing',
    expenseType: 'Advertising Expenses',
    disputeRaisedOn: '2024-07-08',
    disputeAmount: 275.0,
    rejectedBy: 'Manager',
    currentStatus: 'Pending',
  },
  {
    id: '9',
    name: 'Sarah Johnson',
    department: 'Sales',
    expenseType: 'Client Meeting',
    disputeRaisedOn: '2024-07-05',
    disputeAmount: 195.0,
    rejectedBy: 'Finance',
    currentStatus: 'In review',
  },
  {
    id: '10',
    name: 'Michael Brown',
    department: 'Engineering',
    expenseType: 'Equipment Purchase',
    disputeRaisedOn: '2024-07-03',
    disputeAmount: 420.0,
    rejectedBy: 'Manager',
    currentStatus: 'Approved',
  },
  {
    id: '11',
    name: 'Rachel Green',
    department: 'Marketing',
    expenseType: 'Campaign Expenses',
    disputeRaisedOn: '2024-07-02',
    disputeAmount: 225.0,
    rejectedBy: 'Finance',
    currentStatus: 'In review',
  },
  {
    id: '12',
    name: 'Tom Wilson',
    department: 'Sales',
    expenseType: 'Travel Expenses',
    disputeRaisedOn: '2024-07-01',
    disputeAmount: 190.0,
    rejectedBy: 'Manager',
    currentStatus: 'Pending',
  },
  {
    id: '13',
    name: 'Anna Davis',
    department: 'Engineering',
    expenseType: 'Technical Conference',
    disputeRaisedOn: '2024-06-30',
    disputeAmount: 380.0,
    rejectedBy: 'Finance',
    currentStatus: 'Approved',
  },
];


