import React from 'react';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string;
  change: number;
  isTime?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, change, isTime = false }) => {
  const changeColor = change > 0 ? 'text-green-500' : 'text-red-500';
  const changeIcon =
    change > 0 ? (
      <TrendingUp className="w-4 h-4" />
    ) : (
      <TrendingDown className="w-4 h-4" />
    );
  const changeText =
    change > 0
      ? `+${change}${isTime ? ' day' : '%'}`
      : `${change}${isTime ? ' day' : '%'}`;

  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-gray-500 text-sm font-medium">{title}</h3>
      </div>
      <div className="text-2xl font-bold text-gray-900">{value}</div>
      <div className={`flex items-center gap-1 text-sm font-medium mt-2 ${changeColor}`}>
        {changeIcon}
        {changeText}
      </div>
    </div>
  );
};

export default StatCard;
