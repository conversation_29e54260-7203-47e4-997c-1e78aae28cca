import { User, FileText, CheckCircle, Settings } from 'lucide-react';

export const dashboardStats = {
  totalExpensesClaimed: 1250000,
  totalReimbursementsPaid: 1100000,
  pendingRequests: 75,
  averageReimburseTime: 3,
  expensesClaimedChange: 10,
  reimbursementsPaidChange: 8,
  pendingRequestsChange: -5,
  averageTimeChange: -1,
};

export const expenseTrendData = [
  { month: 'SEP', value: 115 },
  { month: 'OCT', value: 95 },
  { month: 'NOV', value: 165 },
  { month: 'DEC', value: 95 },
  { month: 'JAN', value: 110 },
  { month: 'FEB', value: 115 },
];

export const recentActivity = [
  {
    id: '1',
    title: 'New Expense Report',
    submittedBy: '<PERSON>',
    date: '2024-07-28',
  },
  {
    id: '2',
    title: 'Travel Reimbursement',
    submittedBy: '<PERSON>',
    date: '2024-07-27',
  },
  {
    id: '3',
    title: 'Client Meeting Summary',
    submittedBy: '<PERSON>',
    date: '2024-07-26',
  },
  {
    id: '4',
    title: 'Budget Request Submitted',
    submittedBy: '<PERSON>',
    date: '2024-07-25',
  },
  {
    id: '5',
    title: 'Project Milestone Reached',
    submittedBy: 'Ava <PERSON>',
    date: '2024-07-25',
  },
  {
    id: '6',
    title: 'Leave Application',
    submittedBy: 'Sophia Jones',
    date: '2024-07-24',
  },
  {
    id: '7',
    title: 'Training Completed',
    submittedBy: 'Mason Garcia',
    date: '2024-07-24',
  },
  {
    id: '8',
    title: 'Policy Update Acknowledged',
    submittedBy: 'Isabella Martinez',
    date: '2024-07-23',
  },
  {
    id: '9',
    title: 'Asset Allocation',
    submittedBy: 'James Davis',
    date: '2024-07-23',
  },
  {
    id: '10',
    title: 'Annual Review Uploaded',
    submittedBy: 'Charlotte Rodriguez',
    date: '2024-07-22',
  },
  {
    id: '11',
    title: 'Code of Conduct Signed',
    submittedBy: 'Benjamin Wilson',
    date: '2024-07-22',
  },
  {
    id: '12',
    title: 'Travel Plan Submitted',
    submittedBy: 'Amelia Lee',
    date: '2024-07-21',
  },
  {
    id: '13',
    title: 'Vendor Contract Approved',
    submittedBy: 'Elijah Walker',
    date: '2024-07-21',
  },
  {
    id: '14',
    title: 'Performance Report Finalized',
    submittedBy: 'Mia Hall',
    date: '2024-07-20',
  },
  {
    id: '15',
    title: 'Department Meeting Scheduled',
    submittedBy: 'Lucas Allen',
    date: '2024-07-20',
  },
];

export const userActivity = [
  {
    id: '1',
    type: 'user_registered',
    title: 'New user registered: Sophia Turner',
    subtitle: '2 hours ago',
    icon: User,
  },
  {
    id: '2',
    type: 'expense_submitted',
    title: 'High-value expense report submitted by Liam Harper',
    subtitle: '4 hours ago',
    icon: FileText,
  },
  {
    id: '3',
    type: 'reimbursement_approved',
    title: 'Reimbursement request approved for Ava Bennett',
    subtitle: '6 hours ago',
    icon: CheckCircle,
  },
  {
    id: '4',
    type: 'system_updated',
    title: 'System settings updated by Admin',
    subtitle: '1 day ago',
    icon: Settings,
  },
  {
    id: '5',
    type: 'policy_updated',
    title: 'Expense policy updated',
    subtitle: '2 days ago',
    icon: FileText,
  },
];


export const policyCompliance = {
  percentage: 66,
};
