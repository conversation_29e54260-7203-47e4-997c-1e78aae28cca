import { useState } from 'react';
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';
import { Button } from '../../components/Button';
import AddCategoryModal from './AddCategoryModal';

interface Category {
  id: string;
  icon: string;
  iconBg: string;
  name: string;
  description: string;
  status: 'Active' | 'Inactive';
}

const Categories = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [categories, setCategories] = useState<Category[]>([
    {
      id: '1',
      icon: '/planeIcon.svg',
      iconBg: 'bg-teal-600',
      name: 'Travel & Transportation',
      description: 'Covers travel costs for business trips, including transportation, accommodation, and meals.',
      status: 'Active',
    },
    {
      id: '2',
      icon: '/burgerCategoryIcon.svg',
      iconBg: 'bg-teal-600',
      name: 'Meals & Entertainment',
      description: 'Reimbursement for meals during business travel or client meetings.',
      status: 'Active',
    },
    {
      id: '3',
      icon: '/penIcon.svg',
      iconBg: 'bg-teal-600',
      name: 'Office Supplies',
      description: 'Covers the purchase of necessary office supplies.',
      status: 'Active',
    },
    {
      id: '4',
      icon: '/certificateIcon.svg',
      iconBg: 'bg-teal-600',
      name: 'Training & Professional Services',
      description: 'Reimbursement for professional development courses and training programs.',
      status: 'Active',
    },
    {
      id: '5',
      icon: '/miscIcon.svg',
      iconBg: 'bg-teal-600',
      name: 'Miscellaneous',
      description: 'Petty cash, office refreshments, other ad-hoc expenses',
      status: 'Active',
    },
    {
      id: '6',
      icon: '/lodgingIcon.svg',
      iconBg: 'bg-teal-600',
      name: 'Lodging',
      description: 'Hotels, serviced apartments, conference accommodations',
      status: 'Inactive',
    },
  ]);

  const handleAddCategory = (newCategory: { name: string; description: string; icon: string }) => {
    const category: Category = {
      id: (categories.length + 1).toString(),
      icon: newCategory.icon,
      iconBg: 'bg-teal-600',
      name: newCategory.name,
      description: newCategory.description,
      status: 'Active',
    };
    setCategories([...categories, category]);
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-500">Categories</h1>
          <Breadcrumb pageName="Categories" />
        </div>
        <Button 
          className="bg-slate-700 hover:bg-slate-800 text-white py-2 px-4 rounded-md"
          onClick={() => setIsModalOpen(true)}
        >
          + Add Category
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr className="border-b">
              <th className="py-4 px-6 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Icon</th>
              <th className="py-4 px-6 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Category Name</th>
              <th className="py-4 px-6 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th className="py-4 px-6 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="py-4 px-6 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {categories.map((category) => (
              <tr key={category.id} className="hover:bg-gray-50">
                <td className="py-6 px-6">
                  <div className={`w-12 h-12 ${category.iconBg} rounded-lg flex items-center justify-center`}>
                    <img src={category.icon} alt={category.name} className="w-6 h-6 filter brightness-0 invert" />
                  </div>
                </td>
                <td className="py-6 px-6">
                  <div className="text-sm font-medium text-gray-900">{category.name}</div>
                </td>
                <td className="py-6 px-6">
                  <div className="text-sm text-gray-500 max-w-md">{category.description}</div>
                </td>
                <td className="py-6 px-6">
                  <span
                    className={`inline-flex px-3 py-1 rounded-full text-xs font-medium ${
                      category.status === 'Active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {category.status}
                  </span>
                </td>
                <td className="py-6 px-6">
                  <button className="text-sm text-gray-600 hover:text-gray-900 font-medium">
                    Edit
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <AddCategoryModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleAddCategory}
      />
    </div>
  );
};

export default Categories;