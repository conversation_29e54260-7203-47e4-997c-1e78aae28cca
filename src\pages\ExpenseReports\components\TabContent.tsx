import { useState } from 'react';
import { FiFilter, FiCalendar } from 'react-icons/fi';
import { Button } from '../../../components/Button';
import type { ExpenseData, ApprovalData, DisputeData } from '../../../mockData/mockData';
import Pagination from '../../../components/Pagination';
import Dropdown from '../../../components/Dropdown';
import AllExpenseTable from './AllExpenseTable';
import ApprovalTable from './ApprovalTable';
import DisputesTable from './DisputesTable';

interface TabContentProps {
  activeTab: string;
  tabCategory: string;
  expenseData?: ExpenseData[];
  approvalData?: ApprovalData[];
  disputeData?: DisputeData[];
  getStatusBadgeClass: (status: string) => string;
}

const ITEMS_PER_PAGE = 5;

const TabContent = ({
  activeTab,
  tabCategory,
  expenseData,
  approvalData,
  disputeData,
  getStatusBadgeClass,
}: TabContentProps) => {
  const [currentPage, setCurrentPage] = useState(1);

  const getTabTitle = () => {
    switch (tabCategory) {
      case 'all':
        return 'All Expenses';
      case 'approvals':
        return 'Management Approvals';
      case 'disputes':
        return 'Raised Disputes';
      default:
        return 'All Expenses';
    }
  };

  const getCurrentData = () => {
    switch (tabCategory) {
      case 'all':
        return expenseData || [];
      case 'approvals':
        return approvalData || [];
      case 'disputes':
        return disputeData || [];
      default:
        return expenseData || [];
    }
  };

  const currentData = getCurrentData();
  const totalPages = Math.ceil(currentData.length / ITEMS_PER_PAGE);
  const paginatedData = currentData.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  return (
    <div className={`${activeTab === tabCategory ? 'block' : 'hidden'}`}>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
            <h2 className="text-lg font-semibold text-gray-900">{getTabTitle()}</h2>
            <Button className="border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 w-full sm:w-auto">
              <FiFilter className="w-4 h-4" />
              Filters
            </Button>
          </div>

          <div className="hidden lg:block overflow-x-auto">
            {tabCategory === 'all' && (
              <AllExpenseTable />
            )}

            {tabCategory === 'approvals' && (
              <ApprovalTable />
            )}

            {tabCategory === 'disputes' && (
              <DisputesTable /
            )}
          </div>

          <div className="lg:hidden space-y-4">
            {tabCategory === 'all' && paginatedData.length > 0 ? (
              paginatedData.map((expense) => {
                const expenseItem = expense as ExpenseData;
                return (
                  <div key={expenseItem.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-medium text-gray-900 text-sm">{expenseItem.employeeName}</h3>
                        <p className="text-xs text-gray-500">{expenseItem.department}</p>
                      </div>
                      <span className={getStatusBadgeClass(expenseItem.approvalStatus)}>
                        {expenseItem.approvalStatus}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div>
                        <span className="text-gray-500">Date:</span>
                        <p className="text-gray-900">{expenseItem.submissionDate}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Amount:</span>
                        <p className="text-gray-900 font-medium">${expenseItem.totalAmount.toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Payment:</span>
                        <p className="text-gray-900">{expenseItem.paymentMethod}</p>
                      </div>
                      <div className="flex justify-end">
                        <button className="text-xs text-gray-500 hover:text-gray-700 bg-white px-3 py-1 rounded border">
                          Mark Action
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : tabCategory === 'approvals' && paginatedData.length > 0 ? (
              paginatedData.map((approval) => {
                const approvalItem = approval as ApprovalData;
                return (
                  <div key={approvalItem.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-medium text-gray-900 text-sm">{approvalItem.employeeName}</h3>
                        <p className="text-xs text-gray-500">{approvalItem.department} • {approvalItem.designation}</p>
                      </div>
                      <button className="bg-blue-100 text-blue-700 px-3 py-1 rounded text-xs">
                        View
                      </button>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div>
                        <span className="text-gray-500">Category:</span>
                        <p className="text-gray-900">{approvalItem.expenseCategory}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Date:</span>
                        <p className="text-gray-900">{approvalItem.submissionDate}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Amount:</span>
                        <p className="text-gray-900 font-medium">${approvalItem.totalAmount.toFixed(2)}</p>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : tabCategory === 'disputes' && paginatedData.length > 0 ? (
              paginatedData.map((dispute) => {
                const disputeItem = dispute as DisputeData;
                return (
                  <div key={disputeItem.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-medium text-gray-900 text-sm">{disputeItem.name}</h3>
                        <p className="text-xs text-gray-500">{disputeItem.department}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        disputeItem.currentStatus === 'Approved'
                          ? 'bg-green-100 text-green-800'
                          : disputeItem.currentStatus === 'Pending'
                          ? 'bg-gray-100 text-gray-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {disputeItem.currentStatus}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div>
                        <span className="text-gray-500">Type:</span>
                        <p className="text-gray-900">{disputeItem.expenseType}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Raised On:</span>
                        <p className="text-gray-900">{disputeItem.disputeRaisedOn}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Amount:</span>
                        <p className="text-gray-900 font-medium">${disputeItem.disputeAmount.toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Rejected By:</span>
                        <p className="text-gray-900">{disputeItem.rejectedBy}</p>
                      </div>
                      <div className="col-span-2 flex justify-end">
                        <button className="text-xs text-blue-700 hover:text-blue-800 bg-blue-100 px-3 py-1 rounded">
                          View Details
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="py-8 text-center text-gray-500">
                No data found for this category
              </div>
            )}
          </div>

          {totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={(page) => setCurrentPage(page)}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default TabContent;
