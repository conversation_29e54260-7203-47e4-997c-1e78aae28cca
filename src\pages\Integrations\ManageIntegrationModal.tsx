import { useState } from 'react';
import { FiCheckCircle } from 'react-icons/fi';

interface ManageIntegrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  integration: {
    id: string;
    name: string;
  } | null;
}

const ManageIntegrationModal = ({ isOpen, onClose, integration }: ManageIntegrationModalProps) => {
  const [syncEnabled, setSyncEnabled] = useState(true);
  const [syncFrequency, setSyncFrequency] = useState<'hourly' | 'daily' | 'manual'>('hourly');
  
  if (!isOpen || !integration) return null;

  const handleSaveChanges = () => {
    // Save changes logic here
    console.log('Saving changes for integration:', integration.id);
    console.log('Sync enabled:', syncEnabled);
    console.log('Sync frequency:', syncFrequency);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl mx-4">
        <div className="p-8">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-xl font-bold text-indigo-900">Manage Integration</h2>
            <button 
              onClick={onClose}
              className="text-red-500 hover:text-red-700 font-medium"
            >
              Close
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
            {/* Left Column */}
            <div>
              {/* Integration Status */}
              <div className="mb-8">
                <h3 className="text-gray-500 font-medium mb-4">Integration Status</h3>
                <div className="flex items-center">
                  <div className="bg-green-100 p-1 rounded-full mr-2">
                    <FiCheckCircle className="text-green-500 w-5 h-5" />
                  </div>
                  <span className="text-gray-700">Connected</span>
                </div>
              </div>

              {/* Connection Details */}
              <div className="mb-8">
                <h3 className="text-gray-500 font-medium mb-4">Connection Details</h3>
                <div className="mb-4">
                  <p className="text-gray-700 font-medium">Connected Account</p>
                  <p className="text-gray-500">Tech Solutions Inc.</p>
                </div>
                <div>
                  <p className="text-gray-700 font-medium">Last Connected</p>
                  <p className="text-gray-500">July 20, 2025, 09:30 AM</p>
                </div>
              </div>

              {/* Reconnect Button */}
              <div className="mt-8">
                <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-6 rounded-md transition-colors">
                  Reconnect
                </button>
              </div>
            </div>

            {/* Right Column */}
            <div>
              {/* Synchronization Settings */}
              <div className="mb-8">
                <h3 className="text-gray-500 font-medium mb-4">Synchronization Settings</h3>
                <div className="flex items-center justify-between mb-6">
                  <span className="text-gray-700">Enable Automatic Sync</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      className="sr-only peer" 
                      checked={syncEnabled}
                      onChange={() => setSyncEnabled(!syncEnabled)}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
                  </label>
                </div>

                {/* Sync Frequency */}
                <div className="flex space-x-2 mb-8">
                  <button 
                    className={`px-4 py-1 rounded-md text-sm ${syncFrequency === 'hourly' ? 'bg-gray-200 text-gray-800' : 'bg-white text-gray-500 border border-gray-200'}`}
                    onClick={() => setSyncFrequency('hourly')}
                  >
                    Hourly
                  </button>
                  <button 
                    className={`px-4 py-1 rounded-md text-sm ${syncFrequency === 'daily' ? 'bg-gray-200 text-gray-800' : 'bg-white text-gray-500 border border-gray-200'}`}
                    onClick={() => setSyncFrequency('daily')}
                  >
                    Daily
                  </button>
                  <button 
                    className={`px-4 py-1 rounded-md text-sm ${syncFrequency === 'manual' ? 'bg-gray-200 text-gray-800' : 'bg-white text-gray-500 border border-gray-200'}`}
                    onClick={() => setSyncFrequency('manual')}
                  >
                    Manual
                  </button>
                </div>

                {/* Data Mapping */}
                <div className="mb-8">
                  <h3 className="text-gray-500 font-medium mb-4">Data Mapping</h3>
                  <div className="mb-2">
                    <label className="block text-gray-700 mb-2">Select Data to Sync</label>
                    <div className="relative">
                      <select className="w-full border border-gray-300 rounded-md py-2 px-3 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent appearance-none">
                        <option value="">Choose data</option>
                        <option value="all">All Data</option>
                        <option value="expenses">Expenses Only</option>
                        <option value="invoices">Invoices Only</option>
                        <option value="custom">Custom Selection</option>
                      </select>
                      <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                        <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                          <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3 mt-8">
                  <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors">
                    Test Connection
                  </button>
                  <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors">
                    View Logs
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="mt-8 flex justify-end">
            <button 
              onClick={handleSaveChanges}
              className="bg-teal-700 hover:bg-teal-800 text-white font-medium py-2 px-6 rounded-md transition-colors"
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManageIntegrationModal;