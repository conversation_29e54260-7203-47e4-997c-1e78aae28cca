import React from 'react'

const DisputesTable = () => {
  return (
              <div>
                <div className="flex flex-wrap gap-4 mb-6">
                  <div className="flex items-center gap-2">
                    <FiCalendar className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">Date Range</span>
                  </div>
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Multiple Expenses', 'Conference Expenses', 'Software Licenses', 'Travel Expenses']}
                    label="Expense Type"
                    className="min-w-[140px]"
                  />
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Marketing', 'Sales', 'Engineering', 'Development', 'HR']}
                    label="Department"
                    className="min-w-[120px]"
                  />
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Pending', 'Approved', 'In review']}
                    label="Status"
                    className="min-w-[120px]"
                  />
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Manager', 'Finance']}
                    label="Approval Status"
                    className="min-w-[140px]"
                  />
                </div>

                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Name</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Department</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Expense type</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Dispute Raised On</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Dispute Amount</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Rejected By</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Current Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedData.length > 0 ? (
                      paginatedData.map((dispute) => {
                        const disputeItem = dispute as DisputeData;
                        return (
                          <tr key={disputeItem.id} className="border-b border-gray-100 hover:bg-gray-50">
                            <td className="py-4 px-4 text-sm text-gray-900">{disputeItem.name}</td>
                            <td className="py-4 px-4 text-sm text-gray-600">{disputeItem.department}</td>
                            <td className="py-4 px-4 text-sm text-gray-600 flex items-center gap-2">
                              <span className="w-6 h-6 bg-gray-800 rounded flex items-center justify-center">
                                <img src='categoryIcon.svg' alt="Category Icon" />
                              </span>
                              {disputeItem.expenseType}
                            </td>
                            <td className="py-4 px-4 text-sm text-gray-600">{disputeItem.disputeRaisedOn}</td>
                            <td className="py-4 px-4 text-sm text-gray-900 font-medium">
                              ${disputeItem.disputeAmount.toFixed(2)}
                            </td>
                            <td className="py-4 px-4 text-sm text-gray-600">{disputeItem.rejectedBy}</td>
                            <td className="py-4 px-4">
                              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                disputeItem.currentStatus === 'Approved'
                                  ? 'bg-green-100 text-green-800'
                                  : disputeItem.currentStatus === 'Pending'
                                  ? 'bg-gray-100 text-gray-800'
                                  : 'bg-blue-100 text-blue-800'
                              }`}>
                                {disputeItem.currentStatus}
                              </span>
                            </td>
                            <td className="py-4 px-4">
                              <button className="bg-blue-100 text-blue-700 px-4 py-2 rounded text-sm hover:bg-blue-200">
                                View Details
                              </button>
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={8} className="py-8 px-4 text-center text-gray-500">
                          No disputes found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
  )
}

export default DisputesTable
