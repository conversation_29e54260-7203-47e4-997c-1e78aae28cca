import React from 'react';

interface GoProCardProps {
  isCollapsed: boolean;
}

const GoProCard: React.FC<GoProCardProps> = ({ isCollapsed }) => {
  return (
    <div
      className={`p-4 transition-all duration-300 ${
        isCollapsed ? 'px-2' : 'px-4'
      }`}
    >
      <div
        className={`relative w-full go-pro-gradient rounded-2xl flex flex-col items-center justify-end ${
          isCollapsed ? 'px-2 py-4' : 'px-4 py-6'
        } text-white shadow-lg transition-all duration-300`}
      >
        <div
          className={`absolute ${
            isCollapsed ? '-top-6 w-12 h-12' : '-top-10 w-20 h-20'
          } rounded-full bg-[#0F2B3E] border-4 border-white flex items-center justify-center transition-all duration-300`}
        >
          <img
            src="/goProIcon.svg"
            alt="goProIcon"
            className={`${
              isCollapsed ? 'h-6' : 'h-10'
            } transition-all duration-300`}
          />
        </div>

        {!isCollapsed && (
          <div
            className={`text-center ${
              isCollapsed ? 'mt-8' : 'mt-14'
            } transition-all duration-300`}
          >
            <h2
              className={`${
                isCollapsed ? 'text-sm' : 'text-base'
              } font-semibold mb-2 transition-all duration-300 ${
                isCollapsed ? 'opacity-0 scale-0' : 'opacity-100 scale-100'
              }`}
            >
              Upgrade to PRO
            </h2>
            <p
              className={`text-xs font-bold text-gray-400 leading-tight transition-all duration-300 ${
                isCollapsed ? 'opacity-0 scale-0' : 'opacity-100 scale-100'
              }`}
            >
              To get access to all features!
              <br />
              Connect with Expenso
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GoProCard;