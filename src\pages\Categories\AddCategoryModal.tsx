import { useState } from 'react';
import { Button } from '../../components/Button';

interface AddCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (category: { name: string; description: string; icon: string }) => void;
}

const AddCategoryModal = ({ isOpen, onClose, onSubmit }: AddCategoryModalProps) => {
  const [categoryName, setCategoryName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedIcon, setSelectedIcon] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  const availableIcons = [
    { name: 'certificate', path: '/certificateIcon.svg' },
    { name: 'pen', path: '/penIcon.svg' },
    { name: 'misc', path: '/miscIcon.svg' },
    { name: 'lodging', path: '/lodgingIcon.svg' },
    { name: 'plane', path: '/planeIcon.svg' },
    { name: 'burger', path: '/burgerCategoryIcon.svg' },
  ];

  const filteredIcons = availableIcons.filter(icon =>
    icon.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (categoryName && description && selectedIcon) {
      onSubmit({
        name: categoryName,
        description,
        icon: selectedIcon,
      });
      // Reset form
      setCategoryName('');
      setDescription('');
      setSelectedIcon('');
      setSearchTerm('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Add New Category</h2>
          <button
            onClick={onClose}
            className="text-red-500 hover:text-red-700 font-medium"
          >
            Close
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category Name
                </label>
                <input
                  type="text"
                  value={categoryName}
                  onChange={(e) => setCategoryName(e.target.value)}
                  placeholder="Enter category name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none"
                  required
                />
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="🔍 Search Icons"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Icons
                </label>
                <div className="flex flex-wrap gap-2 mb-4">
                  {filteredIcons.map((icon) => (
                    <button
                      key={icon.name}
                      type="button"
                      onClick={() => setSelectedIcon(icon.path)}
                      className={`w-12 h-12 rounded-lg flex items-center justify-center border-2 transition-colors ${
                        selectedIcon === icon.path
                          ? 'border-teal-500 bg-teal-50'
                          : 'border-gray-200 hover:border-teal-300'
                      }`}
                    >
                      <div className="w-10 h-10 bg-teal-600 rounded-lg flex items-center justify-center">
                        <img
                          src={icon.path}
                          alt={icon.name}
                          className="w-6 h-6 filter brightness-0 invert"
                        />
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Upload Section */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <div className="space-y-2">
                  <p className="text-gray-600">Drag and drop an image here, or click to browse</p>
                  <p className="text-sm text-gray-400">SVG, PNG, JPG or GIF (max. 800×400px)</p>
                  <Button
                    type="button"
                    className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md mt-2"
                  >
                    Upload
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              type="submit"
              className="bg-slate-700 hover:bg-slate-800 text-white px-8 py-2 rounded-md"
            >
              Submit
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddCategoryModal;