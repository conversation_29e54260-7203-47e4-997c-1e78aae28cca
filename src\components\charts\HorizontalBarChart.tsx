import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
} from 'recharts';

type ChartData = {
  label: string;
  value: number;
};

interface HorizontalBarChartProps {
  data: ChartData[];
  barColorList?: string[];
  maxValue?: number;
}

interface BarShapeProps {
  x: number;
  y: number;
  width: number;
  height: number;
  fill?: string;
  index: number;
  payload: ChartData;
}

interface TooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    color: string;
  }>;
  label?: string;
}

const defaultColors = ['#e5e7eb'];

const HorizontalBarChart: React.FC<HorizontalBarChartProps> = ({
  data,
  barColorList = defaultColors,
  maxValue,
}) => {
  const computedMax = maxValue ?? Math.max(...data.map((d) => d.value));
  const CustomBarShape = (props: BarShapeProps) => {
    const { x, y, width, height, fill } = props;
    return (
      <>
        <rect
          x={x}
          y={y + 2}
          width={width}
          height={height - 4}
          fill={fill}
          rx={1}
          ry={1}
        />
        <rect
          x={x + width - 8}
          y={y + 4}
          width={5}
          height={height - 8}
          fill="#4b5563"
          // fill="#2dd4bf"
        />
      </>
    );
  };

  const CustomTooltip = ({ active, payload, label }: TooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-3 min-w-[120px]">
          <div className="text-sm font-medium text-gray-900 mb-1">
            {label}
          </div>
          <div className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: payload[0].color }}
            />
            <span className="text-sm text-gray-600">
              Value: <span className="font-semibold text-teal-600">{payload[0].value}</span>
            </span>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={data.length * 40}>
      <BarChart
        layout="vertical"
        data={data}
        margin={{ top: 10, right: 20, left: 20, bottom: 10 }}
      >
        <XAxis
          type="number"
          domain={[0, computedMax]}
          axisLine={false}
          tickLine={false}
          hide
        />
        <YAxis
          dataKey="label"
          type="category"
          width={100}
          tick={{ fontSize: 12 }}
          axisLine={false}
          tickLine={false}
        />
        <Tooltip
          content={<CustomTooltip />}
          cursor={{ fill: 'rgba(20, 184, 166, 0.1)' }}
        />
        <Bar
          dataKey="value"
          shape={(props: unknown) => {
            const barProps = props as BarShapeProps;
            return (
              <CustomBarShape {...barProps} fill={barColorList[barProps.index % barColorList.length]} />
            );
          }}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default HorizontalBarChart;
