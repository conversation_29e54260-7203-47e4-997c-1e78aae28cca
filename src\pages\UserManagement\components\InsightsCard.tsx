const InsightsCard = () => {
  return (
    <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-md">
      <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-6">Insights</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-2 gap-4 sm:gap-6">

        <div className="bg-gray-50 rounded-xl p-4 border border-gray-200">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm text-gray-600">Compliance Rate</span>
            <span className="text-sm text-green-600 font-medium">+2%</span>
          </div>
          <div className="text-2xl font-bold text-gray-900">95%</div>
        </div>

        <div className="bg-gray-50 rounded-xl p-4 border border-gray-200">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm text-gray-600">Top Violation Type</span>
            <span className="text-sm text-gray-600">10</span>
          </div>
          <div className="text-lg font-semibold text-teal-600">Travel Expenses</div>
        </div>

        <div className="bg-gray-50 rounded-xl p-4 border border-gray-200 sm:col-span-2 xl:col-span-1">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm text-gray-600">Violation Rate</span>
            <span className="text-sm text-red-600 font-medium">+2%</span>
          </div>
          <div className="text-2xl font-bold text-gray-900">5%</div>
        </div>
      </div>
    </div>
  );
};

export default InsightsCard;
